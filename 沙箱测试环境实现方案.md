# OpenAPI沙箱测试环境实现方案

## 1. 概述

### 1.1 目标
为OpenAPI-V2.0服务组件提供完整的沙箱测试环境，支持第三方合作伙伴在隔离的环境中进行API接口的模拟测试、联调验证和性能测试，确保不影响生产环境的情况下完成API对接和测试。

### 1.2 核心特性
- **环境隔离**：完全独立的测试环境，与生产环境数据隔离
- **数据模拟**：提供丰富的测试数据模板和场景配置
- **多种测试**：支持连通性测试、功能测试、性能测试
- **实时监控**：提供详细的测试报告和日志分析
- **自动化支持**：支持批量测试和自动化测试脚本

## 2. 架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    开发者门户                                │
├─────────────────────────────────────────────────────────────┤
│  测试控制台  │  测试配置  │  结果展示  │  日志查看  │  报告导出  │
├─────────────────────────────────────────────────────────────┤
│                   沙箱网关层                                │
├─────────────────────────────────────────────────────────────┤
│  请求路由  │  数据模拟  │  流量控制  │  监控记录  │  结果收集  │
├─────────────────────────────────────────────────────────────┤
│                  沙箱服务层                                 │
├─────────────────────────────────────────────────────────────┤
│  模拟服务  │  测试引擎  │  数据生成  │  性能监控  │  日志收集  │
├─────────────────────────────────────────────────────────────┤
│                  沙箱数据层                                 │
├─────────────────────────────────────────────────────────────┤
│  测试数据库  │  缓存系统  │  文件存储  │  日志存储  │  配置存储  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 沙箱网关 (Sandbox Gateway)
- **功能**：请求路由、流量控制、数据转换
- **技术栈**：Spring Cloud Gateway
- **特性**：
  - 支持多租户隔离
  - 请求/响应数据拦截和记录
  - 流量限制和熔断保护
  - 动态路由配置

#### 2.2.2 测试引擎 (Test Engine)
- **功能**：测试用例执行、结果收集、报告生成
- **技术栈**：Spring Boot + TestNG/JUnit
- **特性**：
  - 支持多种测试类型
  - 并发测试执行
  - 实时结果反馈
  - 自动化测试脚本

#### 2.2.3 数据模拟器 (Data Simulator)
- **功能**：测试数据生成、场景模拟、响应模拟
- **技术栈**：Spring Boot + Faker.js
- **特性**：
  - 多种数据类型支持
  - 场景化数据模板
  - 动态数据生成
  - 数据关联性维护

#### 2.2.4 监控收集器 (Monitor Collector)
- **功能**：性能监控、日志收集、指标统计
- **技术栈**：Micrometer + Prometheus + ELK
- **特性**：
  - 实时性能监控
  - 多维度指标收集
  - 异常检测和告警
  - 历史数据分析

## 3. 详细实现

### 3.1 环境隔离实现

#### 3.1.1 数据库隔离
```sql
-- 沙箱数据库配置
CREATE DATABASE openapi_sandbox;

-- 租户数据隔离表
CREATE TABLE sandbox_tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) UNIQUE NOT NULL,
    tenant_name VARCHAR(100) NOT NULL,
    database_schema VARCHAR(64) NOT NULL,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 测试环境配置表
CREATE TABLE sandbox_environments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) NOT NULL,
    env_name VARCHAR(50) NOT NULL,
    env_config JSON,
    base_url VARCHAR(255),
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id)
);
```

#### 3.1.2 网络隔离
```yaml
# Docker Compose 配置
version: '3.8'
services:
  sandbox-gateway:
    image: openapi/sandbox-gateway:latest
    networks:
      - sandbox-network
    environment:
      - SPRING_PROFILES_ACTIVE=sandbox
      - DATABASE_URL=********************************************
    
  sandbox-services:
    image: openapi/sandbox-services:latest
    networks:
      - sandbox-network
    depends_on:
      - sandbox-db
      - sandbox-redis
    
  sandbox-db:
    image: mysql:8.0
    networks:
      - sandbox-network
    environment:
      - MYSQL_DATABASE=openapi_sandbox
      - MYSQL_ROOT_PASSWORD=sandbox_password
    volumes:
      - sandbox_db_data:/var/lib/mysql
    
  sandbox-redis:
    image: redis:6.2
    networks:
      - sandbox-network
    volumes:
      - sandbox_redis_data:/data

networks:
  sandbox-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  sandbox_db_data:
  sandbox_redis_data:
```

### 3.2 沙箱网关实现

#### 3.2.1 网关配置
```java
@Configuration
@EnableConfigurationProperties(SandboxGatewayProperties.class)
public class SandboxGatewayConfig {
    
    @Bean
    public RouteLocator sandboxRoutes(RouteLocatorBuilder builder, 
                                     SandboxGatewayProperties properties) {
        return builder.routes()
            // 沙箱API路由
            .route("sandbox-api", r -> r
                .path("/sandbox/api/**")
                .filters(f -> f
                    .stripPrefix(1)
                    .filter(sandboxAuthFilter())
                    .filter(sandboxLoggingFilter())
                    .filter(sandboxDataMockFilter())
                    .requestRateLimiter(config -> config
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(tenantKeyResolver())
                    )
                )
                .uri(properties.getBackendUrl())
            )
            // 测试工具路由
            .route("sandbox-tools", r -> r
                .path("/sandbox/tools/**")
                .filters(f -> f
                    .stripPrefix(1)
                    .filter(sandboxAuthFilter())
                )
                .uri(properties.getToolsUrl())
            )
            .build();
    }
    
    @Bean
    public SandboxAuthFilter sandboxAuthFilter() {
        return new SandboxAuthFilter();
    }
    
    @Bean
    public SandboxLoggingFilter sandboxLoggingFilter() {
        return new SandboxLoggingFilter();
    }
    
    @Bean
    public SandboxDataMockFilter sandboxDataMockFilter() {
        return new SandboxDataMockFilter();
    }
}
```

#### 3.2.2 认证过滤器
```java
@Component
public class SandboxAuthFilter implements GatewayFilter {
    
    @Autowired
    private SandboxAuthService authService;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 提取认证信息
        String apiKey = request.getHeaders().getFirst("X-API-Key");
        String tenantId = request.getHeaders().getFirst("X-Tenant-Id");
        
        if (StringUtils.isEmpty(apiKey) || StringUtils.isEmpty(tenantId)) {
            return handleAuthError(exchange, "Missing authentication headers");
        }
        
        // 验证沙箱权限
        return authService.validateSandboxAccess(tenantId, apiKey)
            .flatMap(authResult -> {
                if (authResult.isValid()) {
                    // 添加认证信息到请求头
                    ServerHttpRequest mutatedRequest = request.mutate()
                        .header("X-Sandbox-User-Id", authResult.getUserId())
                        .header("X-Sandbox-Tenant-Id", tenantId)
                        .build();
                    
                    return chain.filter(exchange.mutate().request(mutatedRequest).build());
                } else {
                    return handleAuthError(exchange, "Invalid credentials");
                }
            })
            .onErrorResume(throwable -> 
                handleAuthError(exchange, "Authentication service error")
            );
    }
    
    private Mono<Void> handleAuthError(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json");
        
        String body = String.format(
            "{\"code\":401,\"message\":\"%s\",\"timestamp\":\"%s\"}", 
            message, Instant.now().toString()
        );
        
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes());
        return response.writeWith(Mono.just(buffer));
    }
}
```

#### 3.2.3 日志记录过滤器
```java
@Component
public class SandboxLoggingFilter implements GatewayFilter {
    
    @Autowired
    private SandboxLogService logService;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String requestId = UUID.randomUUID().toString();
        
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        
        // 添加请求ID到响应头
        exchange.getResponse().getHeaders().add("X-Request-Id", requestId);
        
        // 记录请求日志
        SandboxRequestLog requestLog = SandboxRequestLog.builder()
            .requestId(requestId)
            .tenantId(request.getHeaders().getFirst("X-Sandbox-Tenant-Id"))
            .method(request.getMethod().name())
            .path(request.getPath().value())
            .queryParams(request.getQueryParams().toString())
            .headers(filterSensitiveHeaders(request.getHeaders()))
            .clientIp(getClientIp(request))
            .userAgent(request.getHeaders().getFirst("User-Agent"))
            .startTime(startTime)
            .build();
        
        return chain.filter(exchange)
            .doFinally(signalType -> {
                // 记录响应日志
                long endTime = System.currentTimeMillis();
                ServerHttpResponse response = exchange.getResponse();
                
                SandboxResponseLog responseLog = SandboxResponseLog.builder()
                    .requestId(requestId)
                    .statusCode(response.getStatusCode().value())
                    .responseTime(endTime - startTime)
                    .endTime(endTime)
                    .build();
                
                // 异步保存日志
                logService.saveRequestLog(requestLog)
                    .and(logService.saveResponseLog(responseLog))
                    .subscribe();
            });
    }
    
    private MultiValueMap<String, String> filterSensitiveHeaders(HttpHeaders headers) {
        MultiValueMap<String, String> filtered = new LinkedMultiValueMap<>();
        headers.forEach((key, values) -> {
            if (!isSensitiveHeader(key)) {
                filtered.put(key, values);
            } else {
                filtered.put(key, List.of("***"));
            }
        });
        return filtered;
    }
    
    private boolean isSensitiveHeader(String headerName) {
        return headerName.toLowerCase().contains("authorization") ||
               headerName.toLowerCase().contains("password") ||
               headerName.toLowerCase().contains("secret");
    }
    
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddress() != null ? 
            request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }
}
```

### 3.3 测试引擎实现

#### 3.3.1 测试引擎核心类
```java
@Service
public class SandboxTestEngine {
    
    @Autowired
    private TestCaseRepository testCaseRepository;
    
    @Autowired
    private TestExecutor testExecutor;
    
    @Autowired
    private TestReportService reportService;
    
    @Autowired
    private SandboxDataService dataService;
    
    /**
     * 执行单个测试用例
     */
    public Mono<TestResult> executeTestCase(String tenantId, String testCaseId) {
        return testCaseRepository.findByIdAndTenantId(testCaseId, tenantId)
            .switchIfEmpty(Mono.error(new TestCaseNotFoundException(testCaseId)))
            .flatMap(testCase -> {
                // 准备测试环境
                return prepareTestEnvironment(tenantId, testCase)
                    .then(testExecutor.execute(testCase))
                    .flatMap(result -> {
                        // 保存测试结果
                        return reportService.saveTestResult(result)
                            .thenReturn(result);
                    })
                    .doFinally(signalType -> {
                        // 清理测试环境
                        cleanupTestEnvironment(tenantId, testCase).subscribe();
                    });
            });
    }
    
    /**
     * 执行批量测试
     */
    public Flux<TestResult> executeBatchTests(String tenantId, BatchTestRequest request) {
        return Flux.fromIterable(request.getTestCaseIds())
            .flatMap(testCaseId -> executeTestCase(tenantId, testCaseId)
                .onErrorResume(throwable -> {
                    // 记录错误但继续执行其他测试
                    TestResult errorResult = TestResult.builder()
                        .testCaseId(testCaseId)
                        .status(TestStatus.ERROR)
                        .errorMessage(throwable.getMessage())
                        .build();
                    return Mono.just(errorResult);
                })
            )
            .doOnComplete(() -> {
                // 生成批量测试报告
                reportService.generateBatchReport(tenantId, request.getBatchId())
                    .subscribe();
            });
    }
    
    /**
     * 执行性能测试
     */
    public Mono<PerformanceTestResult> executePerformanceTest(String tenantId, 
                                                             PerformanceTestRequest request) {
        return preparePerformanceTestEnvironment(tenantId, request)
            .then(testExecutor.executePerformanceTest(request))
            .flatMap(result -> {
                return reportService.savePerformanceTestResult(result)
                    .thenReturn(result);
            });
    }
    
    private Mono<Void> prepareTestEnvironment(String tenantId, TestCase testCase) {
        return dataService.prepareTestData(tenantId, testCase.getDataRequirements())
            .then(dataService.setupMockServices(tenantId, testCase.getMockConfigs()));
    }
    
    private Mono<Void> cleanupTestEnvironment(String tenantId, TestCase testCase) {
        return dataService.cleanupTestData(tenantId, testCase.getDataRequirements())
            .then(dataService.teardownMockServices(tenantId, testCase.getMockConfigs()));
    }
    
    private Mono<Void> preparePerformanceTestEnvironment(String tenantId, 
                                                        PerformanceTestRequest request) {
        return dataService.preparePerformanceTestData(tenantId, request.getDataSize())
            .then(dataService.setupPerformanceMonitoring(tenantId));
    }
}
```

#### 3.3.2 测试执行器
```java
@Component
public class TestExecutor {
    
    @Autowired
    private WebClient webClient;
    
    @Autowired
    private TestMetricsCollector metricsCollector;
    
    /**
     * 执行基础连通性测试
     */
    public Mono<TestResult> executeConnectivityTest(TestCase testCase) {
        long startTime = System.currentTimeMillis();
        
        return webClient
            .method(HttpMethod.valueOf(testCase.getMethod()))
            .uri(testCase.getUrl())
            .headers(headers -> headers.addAll(testCase.getHeaders()))
            .bodyValue(testCase.getRequestBody())
            .retrieve()
            .toEntity(String.class)
            .map(response -> {
                long endTime = System.currentTimeMillis();
                return TestResult.builder()
                    .testCaseId(testCase.getId())
                    .status(TestStatus.SUCCESS)
                    .responseTime(endTime - startTime)
                    .statusCode(response.getStatusCodeValue())
                    .responseBody(response.getBody())
                    .responseHeaders(response.getHeaders().toSingleValueMap())
                    .build();
            })
            .onErrorResume(throwable -> {
                long endTime = System.currentTimeMillis();
                return Mono.just(TestResult.builder()
                    .testCaseId(testCase.getId())
                    .status(TestStatus.FAILED)
                    .responseTime(endTime - startTime)
                    .errorMessage(throwable.getMessage())
                    .build());
            });
    }
    
    /**
     * 执行功能测试
     */
    public Mono<TestResult> executeFunctionalTest(TestCase testCase) {
        return executeConnectivityTest(testCase)
            .flatMap(result -> {
                if (result.getStatus() == TestStatus.SUCCESS) {
                    // 验证响应数据
                    return validateResponse(testCase, result)
                        .map(validationResult -> {
                            if (validationResult.isValid()) {
                                return result;
                            } else {
                                return result.toBuilder()
                                    .status(TestStatus.FAILED)
                                    .errorMessage("Response validation failed: " + 
                                                validationResult.getErrorMessage())
                                    .build();
                            }
                        });
                } else {
                    return Mono.just(result);
                }
            });
    }
    
    /**
     * 执行性能测试
     */
    public Mono<PerformanceTestResult> executePerformanceTest(PerformanceTestRequest request) {
        int concurrency = request.getConcurrency();
        int totalRequests = request.getTotalRequests();
        Duration duration = request.getDuration();
        
        List<Mono<TestResult>> requests = new ArrayList<>();
        
        // 创建并发请求
        for (int i = 0; i < totalRequests; i++) {
            requests.add(executeConnectivityTest(request.getTestCase())
                .delayElement(Duration.ofMillis(i * 1000 / request.getRequestsPerSecond()))
            );
        }
        
        long startTime = System.currentTimeMillis();
        
        return Flux.merge(requests, concurrency)
            .collectList()
            .map(results -> {
                long endTime = System.currentTimeMillis();
                return analyzePerformanceResults(results, startTime, endTime, request);
            })
            .timeout(duration.plusSeconds(30)); // 添加超时保护
    }
    
    private Mono<ValidationResult> validateResponse(TestCase testCase, TestResult result) {
        return Mono.fromCallable(() -> {
            // 实现响应验证逻辑
            List<TestAssertion> assertions = testCase.getAssertions();
            
            for (TestAssertion assertion : assertions) {
                if (!assertion.validate(result)) {
                    return ValidationResult.failed(assertion.getErrorMessage());
                }
            }
            
            return ValidationResult.success();
        });
    }
    
    private PerformanceTestResult analyzePerformanceResults(List<TestResult> results, 
                                                           long startTime, long endTime, 
                                                           PerformanceTestRequest request) {
        // 计算性能指标
        List<Long> responseTimes = results.stream()
            .map(TestResult::getResponseTime)
            .sorted()
            .collect(Collectors.toList());
        
        long totalTime = endTime - startTime;
        int successCount = (int) results.stream().filter(r -> r.getStatus() == TestStatus.SUCCESS).count();
        int failureCount = results.size() - successCount;
        
        double tps = (double) results.size() / (totalTime / 1000.0);
        double successRate = (double) successCount / results.size() * 100;
        
        return PerformanceTestResult.builder()
            .testCaseId(request.getTestCase().getId())
            .totalRequests(results.size())
            .successCount(successCount)
            .failureCount(failureCount)
            .successRate(successRate)
            .tps(tps)
            .avgResponseTime(responseTimes.stream().mapToLong(Long::longValue).average().orElse(0))
            .minResponseTime(responseTimes.get(0))
            .maxResponseTime(responseTimes.get(responseTimes.size() - 1))
            .p50ResponseTime(getPercentile(responseTimes, 0.5))
            .p95ResponseTime(getPercentile(responseTimes, 0.95))
            .p99ResponseTime(getPercentile(responseTimes, 0.99))
            .totalTime(totalTime)
            .build();
    }
    
    private long getPercentile(List<Long> sortedValues, double percentile) {
        int index = (int) Math.ceil(sortedValues.size() * percentile) - 1;
        return sortedValues.get(Math.max(0, Math.min(index, sortedValues.size() - 1)));
    }
}
```

### 3.4 数据模拟器实现

#### 3.4.1 数据模拟服务
```java
@Service
public class SandboxDataService {
    
    @Autowired
    private DataTemplateRepository templateRepository;
    
    @Autowired
    private TestDataGenerator dataGenerator;
    
    @Autowired
    private MockServiceManager mockServiceManager;
    
    /**
     * 准备测试数据
     */
    public Mono<Void> prepareTestData(String tenantId, List<DataRequirement> requirements) {
        return Flux.fromIterable(requirements)
            .flatMap(requirement -> {
                return templateRepository.findByTypeAndTenantId(requirement.getDataType(), tenantId)
                    .switchIfEmpty(templateRepository.findByTypeAndTenantId(requirement.getDataType(), "default"))
                    .flatMap(template -> {
                        return dataGenerator.generateData(template, requirement.getCount())
                            .flatMap(data -> saveTestData(tenantId, requirement.getDataType(), data));
                    });
            })
            .then();
    }
    
    /**
     * 设置模拟服务
     */
    public Mono<Void> setupMockServices(String tenantId, List<MockConfig> mockConfigs) {
        return Flux.fromIterable(mockConfigs)
            .flatMap(config -> mockServiceManager.createMockService(tenantId, config))
            .then();
    }
    
    /**
     * 清理测试数据
     */
    public Mono<Void> cleanupTestData(String tenantId, List<DataRequirement> requirements) {
        return Flux.fromIterable(requirements)
            .flatMap(requirement -> deleteTestData(tenantId, requirement.getDataType()))
            .then();
    }
    
    /**
     * 拆除模拟服务
     */
    public Mono<Void> teardownMockServices(String tenantId, List<MockConfig> mockConfigs) {
        return Flux.fromIterable(mockConfigs)
            .flatMap(config -> mockServiceManager.removeMockService(tenantId, config.getServiceId()))
            .then();
    }
    
    private Mono<Void> saveTestData(String tenantId, String dataType, Object data) {
        // 实现数据保存逻辑
        return Mono.fromRunnable(() -> {
            // 保存到租户专用的测试数据库
            // 实现具体的数据保存逻辑
        });
    }
    
    private Mono<Void> deleteTestData(String tenantId, String dataType) {
        // 实现数据清理逻辑
        return Mono.fromRunnable(() -> {
            // 从租户专用的测试数据库删除数据
            // 实现具体的数据清理逻辑
        });
    }
}
```

#### 3.4.2 测试数据生成器
```java
@Component
public class TestDataGenerator {
    
    private final Faker faker = new Faker(Locale.CHINA);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 根据模板生成测试数据
     */
    public Mono<List<Object>> generateData(DataTemplate template, int count) {
        return Mono.fromCallable(() -> {
            List<Object> dataList = new ArrayList<>();
            
            for (int i = 0; i < count; i++) {
                Object data = generateSingleData(template);
                dataList.add(data);
            }
            
            return dataList;
        });
    }
    
    private Object generateSingleData(DataTemplate template) {
        Map<String, Object> data = new HashMap<>();
        
        template.getFields().forEach(field -> {
            Object value = generateFieldValue(field);
            data.put(field.getName(), value);
        });
        
        return data;
    }
    
    private Object generateFieldValue(DataField field) {
        switch (field.getType()) {
            case "string":
                return generateStringValue(field);
            case "number":
                return generateNumberValue(field);
            case "boolean":
                return faker.bool().bool();
            case "date":
                return generateDateValue(field);
            case "email":
                return faker.internet().emailAddress();
            case "phone":
                return faker.phoneNumber().cellPhone();
            case "name":
                return faker.name().fullName();
            case "address":
                return faker.address().fullAddress();
            case "id":
                return generateIdValue(field);
            default:
                return generateCustomValue(field);
        }
    }
    
    private String generateStringValue(DataField field) {
        if (field.hasPattern()) {
            return faker.regexify(field.getPattern());
        } else if (field.hasOptions()) {
            return faker.options().option(field.getOptions().toArray(new String[0]));
        } else {
            int length = field.getLength() != null ? field.getLength() : 10;
            return faker.lorem().characters(length);
        }
    }
    
    private Number generateNumberValue(DataField field) {
        if (field.isInteger()) {
            int min = field.getMin() != null ? field.getMin().intValue() : 0;
            int max = field.getMax() != null ? field.getMax().intValue() : 1000;
            return faker.number().numberBetween(min, max);
        } else {
            double min = field.getMin() != null ? field.getMin().doubleValue() : 0.0;
            double max = field.getMax() != null ? field.getMax().doubleValue() : 1000.0;
            return faker.number().randomDouble(2, (long) min, (long) max);
        }
    }
    
    private String generateDateValue(DataField field) {
        if (field.getDateFormat() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(field.getDateFormat());
            return sdf.format(faker.date().birthday());
        } else {
            return faker.date().birthday().toString();
        }
    }
    
    private String generateIdValue(DataField field) {
        if ("uuid".equals(field.getSubType())) {
            return UUID.randomUUID().toString();
        } else if ("snowflake".equals(field.getSubType())) {
            return String.valueOf(System.currentTimeMillis() * 1000 + faker.number().numberBetween(0, 999));
        } else {
            return String.valueOf(faker.number().numberBetween(100000, 999999));
        }
    }
    
    private Object generateCustomValue(DataField field) {
        // 处理自定义类型
        if (field.getCustomGenerator() != null) {
            // 执行自定义生成器
            return executeCustomGenerator(field.getCustomGenerator());
        } else {
            return faker.lorem().word();
        }
    }
    
    private Object executeCustomGenerator(String generatorScript) {
        // 实现自定义生成器执行逻辑
        // 可以使用脚本引擎或者预定义的生成器
        return "custom_value";
    }
}
```

### 3.5 前端界面实现

#### 3.5.1 测试控制台组件
```typescript
// SandboxTestConsole.tsx
import React, { useState, useEffect } from 'react';
import { Card, Button, Form, Select, Input, Table, Tabs, Progress } from 'antd';
import { PlayCircleOutlined, StopOutlined, DownloadOutlined } from '@ant-design/icons';

interface TestCase {
  id: string;
  name: string;
  type: 'connectivity' | 'functional' | 'performance';
  method: string;
  url: string;
  status: 'pending' | 'running' | 'success' | 'failed';
}

interface TestResult {
  id: string;
  testCaseId: string;
  status: 'success' | 'failed' | 'error';
  responseTime: number;
  statusCode?: number;
  errorMessage?: string;
}

const SandboxTestConsole: React.FC = () => {
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [selectedTestCase, setSelectedTestCase] = useState<string | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    loadTestCases();
  }, []);

  const loadTestCases = async () => {
    try {
      const response = await fetch('/api/sandbox/test-cases');
      const data = await response.json();
      setTestCases(data);
    } catch (error) {
      console.error('Failed to load test cases:', error);
    }
  };

  const executeTestCase = async (testCaseId: string) => {
    setIsRunning(true);
    setProgress(0);

    try {
      const response = await fetch(`/api/sandbox/test-cases/${testCaseId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        setTestResults(prev => [...prev, result]);
        setProgress(100);
      } else {
        throw new Error('Test execution failed');
      }
    } catch (error) {
      console.error('Test execution error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const executeBatchTests = async () => {
    setIsRunning(true);
    setProgress(0);

    const selectedCases = testCases.filter(tc => tc.status === 'pending');
    const totalCases = selectedCases.length;

    for (let i = 0; i < selectedCases.length; i++) {
      await executeTestCase(selectedCases[i].id);
      setProgress(((i + 1) / totalCases) * 100);
    }

    setIsRunning(false);
  };

  const exportResults = () => {
    const dataStr = JSON.stringify(testResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `test-results-${new Date().toISOString()}.json`;
    link.click();
  };

  const testCaseColumns = [
    {
      title: '测试用例名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '测试类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          connectivity: '连通性测试',
          functional: '功能测试',
          performance: '性能测试',
        };
        return typeMap[type as keyof typeof typeMap] || type;
      },
    },
    {
      title: '请求方法',
      dataIndex: 'method',
      key: 'method',
    },
    {
      title: '请求URL',
      dataIndex: 'url',
      key: 'url',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: '待执行',
          running: '执行中',
          success: '成功',
          failed: '失败',
        };
        return statusMap[status as keyof typeof statusMap] || status;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: TestCase) => (
        <Button
          type="primary"
          size="small"
          icon={<PlayCircleOutlined />}
          onClick={() => executeTestCase(record.id)}
          disabled={isRunning}
        >
          执行
        </Button>
      ),
    },
  ];

  const resultColumns = [
    {
      title: '测试用例',
      dataIndex: 'testCaseId',
      key: 'testCaseId',
      render: (testCaseId: string) => {
        const testCase = testCases.find(tc => tc.id === testCaseId);
        return testCase?.name || testCaseId;
      },
    },
    {
      title: '执行状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          success: '成功',
          failed: '失败',
          error: '错误',
        };
        return statusMap[status as keyof typeof statusMap] || status;
      },
    },
    {
      title: '响应时间(ms)',
      dataIndex: 'responseTime',
      key: 'responseTime',
    },
    {
      title: '状态码',
      dataIndex: 'statusCode',
      key: 'statusCode',
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      ellipsis: true,
    },
  ];

  return (
    <div className="sandbox-test-console">
      <Card title="沙箱测试控制台" className="mb-4">
        <div className="mb-4">
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={executeBatchTests}
            disabled={isRunning}
            className="mr-2"
          >
            批量执行
          </Button>
          <Button
            icon={<StopOutlined />}
            disabled={!isRunning}
            className="mr-2"
          >
            停止执行
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={exportResults}
            disabled={testResults.length === 0}
          >
            导出结果
          </Button>
        </div>
        
        {isRunning && (
          <Progress percent={progress} className="mb-4" />
        )}
      </Card>

      <Tabs defaultActiveKey="testCases">
        <Tabs.TabPane tab="测试用例" key="testCases">
          <Table
            columns={testCaseColumns}
            dataSource={testCases}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </Tabs.TabPane>
        
        <Tabs.TabPane tab="测试结果" key="testResults">
          <Table
            columns={resultColumns}
            dataSource={testResults}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};

export default SandboxTestConsole;
```

#### 3.5.2 测试配置组件
```typescript
// TestCaseEditor.tsx
import React, { useState } from 'react';
import { Form, Input, Select, Button, Card, Space, Table } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

interface TestCaseFormData {
  name: string;
  type: 'connectivity' | 'functional' | 'performance';
  method: string;
  url: string;
  headers: { key: string; value: string }[];
  requestBody?: string;
  assertions: { type: string; field: string; operator: string; value: string }[];
}

const TestCaseEditor: React.FC = () => {
  const [form] = Form.useForm();
  const [headers, setHeaders] = useState<{ key: string; value: string }[]>([]);
  const [assertions, setAssertions] = useState<any[]>([]);

  const addHeader = () => {
    setHeaders([...headers, { key: '', value: '' }]);
  };

  const removeHeader = (index: number) => {
    setHeaders(headers.filter((_, i) => i !== index));
  };

  const updateHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...headers];
    newHeaders[index][field] = value;
    setHeaders(newHeaders);
  };

  const addAssertion = () => {
    setAssertions([...assertions, { type: 'response', field: '', operator: 'equals', value: '' }]);
  };

  const removeAssertion = (index: number) => {
    setAssertions(assertions.filter((_, i) => i !== index));
  };

  const updateAssertion = (index: number, field: string, value: string) => {
    const newAssertions = [...assertions];
    newAssertions[index][field] = value;
    setAssertions(newAssertions);
  };

  const onFinish = (values: TestCaseFormData) => {
    const testCase = {
      ...values,
      headers,
      assertions,
    };
    
    console.log('Test case data:', testCase);
    // 提交测试用例数据
  };

  const headerColumns = [
    {
      title: '请求头名称',
      dataIndex: 'key',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          onChange={(e) => updateHeader(index, 'key', e.target.value)}
          placeholder="请求头名称"
        />
      ),
    },
    {
      title: '请求头值',
      dataIndex: 'value',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          onChange={(e) => updateHeader(index, 'value', e.target.value)}
          placeholder="请求头值"
        />
      ),
    },
    {
      title: '操作',
      render: (text: any, record: any, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeHeader(index)}
        />
      ),
    },
  ];

  const assertionColumns = [
    {
      title: '断言类型',
      dataIndex: 'type',
      render: (text: string, record: any, index: number) => (
        <Select
          value={text}
          onChange={(value) => updateAssertion(index, 'type', value)}
          style={{ width: '100%' }}
        >
          <Select.Option value="response">响应断言</Select.Option>
          <Select.Option value="status">状态码断言</Select.Option>
          <Select.Option value="time">响应时间断言</Select.Option>
        </Select>
      ),
    },
    {
      title: '字段',
      dataIndex: 'field',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          onChange={(e) => updateAssertion(index, 'field', e.target.value)}
          placeholder="字段路径"
        />
      ),
    },
    {
      title: '操作符',
      dataIndex: 'operator',
      render: (text: string, record: any, index: number) => (
        <Select
          value={text}
          onChange={(value) => updateAssertion(index, 'operator', value)}
          style={{ width: '100%' }}
        >
          <Select.Option value="equals">等于</Select.Option>
          <Select.Option value="contains">包含</Select.Option>
          <Select.Option value="greater">大于</Select.Option>
          <Select.Option value="less">小于</Select.Option>
        </Select>
      ),
    },
    {
      title: '期望值',
      dataIndex: 'value',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          onChange={(e) => updateAssertion(index, 'value', e.target.value)}
          placeholder="期望值"
        />
      ),
    },
    {
      title: '操作',
      render: (text: any, record: any, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeAssertion(index)}
        />
      ),
    },
  ];

  return (
    <Card title="测试用例编辑器">
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Form.Item
          name="name"
          label="测试用例名称"
          rules={[{ required: true, message: '请输入测试用例名称' }]}
        >
          <Input placeholder="请输入测试用例名称" />
        </Form.Item>

        <Form.Item
          name="type"
          label="测试类型"
          rules={[{ required: true, message: '请选择测试类型' }]}
        >
          <Select placeholder="请选择测试类型">
            <Select.Option value="connectivity">连通性测试</Select.Option>
            <Select.Option value="functional">功能测试</Select.Option>
            <Select.Option value="performance">性能测试</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="method"
          label="请求方法"
          rules={[{ required: true, message: '请选择请求方法' }]}
        >
          <Select placeholder="请选择请求方法">
            <Select.Option value="GET">GET</Select.Option>
            <Select.Option value="POST">POST</Select.Option>
            <Select.Option value="PUT">PUT</Select.Option>
            <Select.Option value="DELETE">DELETE</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="url"
          label="请求URL"
          rules={[{ required: true, message: '请输入请求URL' }]}
        >
          <Input placeholder="请输入请求URL" />
        </Form.Item>

        <Form.Item label="请求头">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button type="dashed" onClick={addHeader} icon={<PlusOutlined />}>
              添加请求头
            </Button>
            {headers.length > 0 && (
              <Table
                columns={headerColumns}
                dataSource={headers}
                pagination={false}
                size="small"
              />
            )}
          </Space>
        </Form.Item>

        <Form.Item name="requestBody" label="请求体">
          <Input.TextArea rows={4} placeholder="请输入请求体（JSON格式）" />
        </Form.Item>

        <Form.Item label="断言配置">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button type="dashed" onClick={addAssertion} icon={<PlusOutlined />}>
              添加断言
            </Button>
            {assertions.length > 0 && (
              <Table
                columns={assertionColumns}
                dataSource={assertions}
                pagination={false}
                size="small"
              />
            )}
          </Space>
        </Form.Item>

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit">
              保存测试用例
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TestCaseEditor;
```

## 4. 部署配置

### 4.1 Docker配置

#### 4.1.1 Dockerfile
```dockerfile
# 沙箱网关 Dockerfile
FROM openjdk:11-jre-slim

VOLUME /tmp

COPY target/sandbox-gateway-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 4.1.2 Docker Compose
```yaml
version: '3.8'

services:
  # 沙箱网关
  sandbox-gateway:
    build: ./sandbox-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=********************************************
      - REDIS_URL=redis://sandbox-redis:6379
    depends_on:
      - sandbox-db
      - sandbox-redis
    networks:
      - sandbox-network

  # 沙箱服务
  sandbox-services:
    build: ./sandbox-services
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=********************************************
      - REDIS_URL=redis://sandbox-redis:6379
    depends_on:
      - sandbox-db
      - sandbox-redis
    networks:
      - sandbox-network

  # 前端应用
  sandbox-frontend:
    build: ./sandbox-frontend
    ports:
      - "3000:80"
    depends_on:
      - sandbox-gateway
    networks:
      - sandbox-network

  # 数据库
  sandbox-db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=sandbox_root_password
      - MYSQL_DATABASE=openapi_sandbox
      - MYSQL_USER=sandbox_user
      - MYSQL_PASSWORD=sandbox_password
    volumes:
      - sandbox_db_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - sandbox-network

  # Redis缓存
  sandbox-redis:
    image: redis:6.2-alpine
    command: redis-server --appendonly yes
    volumes:
      - sandbox_redis_data:/data
    networks:
      - sandbox-network

  # Elasticsearch (日志存储)
  sandbox-elasticsearch:
    image: elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - sandbox_es_data:/usr/share/elasticsearch/data
    networks:
      - sandbox-network

  # Kibana (日志查看)
  sandbox-kibana:
    image: kibana:7.14.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://sandbox-elasticsearch:9200
    depends_on:
      - sandbox-elasticsearch
    networks:
      - sandbox-network

volumes:
  sandbox_db_data:
  sandbox_redis_data:
  sandbox_es_data:

networks:
  sandbox-network:
    driver: bridge
```

### 4.2 Kubernetes配置

#### 4.2.1 命名空间
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: openapi-sandbox
```

#### 4.2.2 配置映射
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: sandbox-config
  namespace: openapi-sandbox
data:
  application.yml: |
    spring:
      profiles:
        active: kubernetes
      datasource:
        url: ***********************************************
        username: sandbox_user
        password: sandbox_password
      redis:
        host: sandbox-redis
        port: 6379
    
    sandbox:
      gateway:
        backend-url: http://sandbox-services:8081
        tools-url: http://sandbox-tools:8082
      test:
        max-concurrent-tests: 100
        default-timeout: 30s
      data:
        cleanup-interval: 1h
        retention-days: 7
```

#### 4.2.3 部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sandbox-gateway
  namespace: openapi-sandbox
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sandbox-gateway
  template:
    metadata:
      labels:
        app: sandbox-gateway
    spec:
      containers:
      - name: sandbox-gateway
        image: openapi/sandbox-gateway:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: sandbox-config
---
apiVersion: v1
kind: Service
metadata:
  name: sandbox-gateway
  namespace: openapi-sandbox
spec:
  selector:
    app: sandbox-gateway
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

## 5. 监控和运维

### 5.1 监控指标

#### 5.1.1 业务指标
- 测试用例执行数量
- 测试成功率
- 平均响应时间
- 并发测试数量
- 数据生成量

#### 5.1.2 系统指标
- CPU使用率
- 内存使用率
- 网络流量
- 磁盘IO
- 数据库连接数

#### 5.1.3 应用指标
- JVM堆内存使用
- GC频率和时间
- 线程池状态
- 缓存命中率
- 错误率

### 5.2 告警配置

```yaml
# Prometheus告警规则
groups:
- name: sandbox.rules
  rules:
  - alert: SandboxHighErrorRate
    expr: rate(sandbox_test_failures_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "沙箱测试错误率过高"
      description: "沙箱环境测试错误率超过10%，持续时间超过2分钟"
  
  - alert: SandboxHighResponseTime
    expr: histogram_quantile(0.95, rate(sandbox_test_duration_seconds_bucket[5m])) > 5
    for: 3m
    labels:
      severity: critical
    annotations:
      summary: "沙箱测试响应时间过长"
      description: "沙箱环境95%测试响应时间超过5秒，持续时间超过3分钟"
  
  - alert: SandboxServiceDown
    expr: up{job="sandbox-gateway"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "沙箱网关服务不可用"
      description: "沙箱网关服务已停止响应超过1分钟"
```

### 5.3 日志管理

#### 5.3.1 日志配置
```yaml
# logback-spring.xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="!kubernetes">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
    
    <springProfile name="kubernetes">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <arguments/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
</configuration>
```

## 6. 安全考虑

### 6.1 数据安全
- **数据隔离**：每个租户的测试数据完全隔离
- **数据加密**：敏感数据传输和存储加密
- **数据清理**：定期清理过期测试数据
- **访问控制**：基于角色的访问控制

### 6.2 网络安全
- **网络隔离**：沙箱环境与生产环境网络隔离
- **防火墙规则**：严格的入站和出站规则
- **VPN访问**：通过VPN访问沙箱环境
- **API限流**：防止恶意请求和DDoS攻击

### 6.3 认证授权
```java
@Configuration
@EnableWebSecurity
public class SandboxSecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/sandbox/public/**").permitAll()
                .requestMatchers("/api/sandbox/test/**").hasRole("TESTER")
                .requestMatchers("/api/sandbox/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .jwtAuthenticationConverter(jwtAuthenticationConverter())
                )
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            .csrf(csrf -> csrf.disable());
        
        return http.build();
    }
    
    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtAuthenticationConverter converter = new JwtAuthenticationConverter();
        converter.setJwtGrantedAuthoritiesConverter(jwt -> {
            Collection<String> authorities = jwt.getClaimAsStringList("authorities");
            return authorities.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
        });
        return converter;
    }
}
```

## 7. 性能优化

### 7.1 缓存策略
```java
@Configuration
@EnableCaching
public class SandboxCacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

### 7.2 连接池优化
```yaml
# 数据库连接池配置
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 20000
      validation-timeout: 5000
      leak-detection-threshold: 60000

# Redis连接池配置
  redis:
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
```

## 8. 使用指南

### 8.1 快速开始

1. **环境准备**
   ```bash
   # 克隆项目
   git clone https://github.com/openapi/sandbox.git
   cd sandbox
   
   # 启动环境
   docker-compose up -d
   ```

2. **创建测试用例**
   - 访问 http://localhost:3000
   - 登录开发者账号
   - 创建新的测试用例
   - 配置请求参数和断言

3. **执行测试**
   - 选择测试用例
   - 点击执行按钮
   - 查看测试结果

### 8.2 API使用示例

#### 8.2.1 创建测试用例
```bash
curl -X POST http://localhost:8080/api/sandbox/test-cases \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -H "X-Tenant-Id: your-tenant-id" \
  -d '{
    "name": "用户查询接口测试",
    "type": "functional",
    "method": "GET",
    "url": "/api/users/123",
    "headers": {
      "Authorization": "Bearer test-token"
    },
    "assertions": [
      {
        "type": "status",
        "operator": "equals",
        "value": "200"
      },
      {
        "type": "response",
        "field": "$.data.id",
        "operator": "equals",
        "value": "123"
      }
    ]
  }'
```

#### 8.2.2 执行测试用例
```bash
curl -X POST http://localhost:8080/api/sandbox/test-cases/test-case-id/execute \
  -H "X-API-Key: your-api-key" \
  -H "X-Tenant-Id: your-tenant-id"
```

#### 8.2.3 查看测试结果
```bash
curl -X GET http://localhost:8080/api/sandbox/test-results/result-id \
  -H "X-API-Key: your-api-key" \
  -H "X-Tenant-Id: your-tenant-id"
```

## 9. 故障排除

### 9.1 常见问题

#### 9.1.1 测试执行失败
**问题**：测试用例执行失败，返回连接超时错误

**解决方案**：
1. 检查目标服务是否正常运行
2. 验证网络连接是否正常
3. 检查防火墙规则
4. 增加超时时间配置

#### 9.1.2 数据生成异常
**问题**：测试数据生成失败或数据格式不正确

**解决方案**：
1. 检查数据模板配置
2. 验证数据生成规则
3. 查看数据生成器日志
4. 更新数据模板

#### 9.1.3 性能测试结果异常
**问题**：性能测试结果显示异常高的响应时间

**解决方案**：
1. 检查系统资源使用情况
2. 分析网络延迟
3. 优化测试并发数
4. 检查目标服务性能

### 9.2 日志分析

#### 9.2.1 查看应用日志
```bash
# 查看网关日志
docker logs sandbox-gateway

# 查看服务日志
docker logs sandbox-services

# 实时查看日志
docker logs -f sandbox-gateway
```

#### 9.2.2 查看系统指标
```bash
# 查看容器资源使用
docker stats

# 查看系统负载
top
htop

# 查看网络连接
netstat -an | grep 8080
```

## 10. 总结

本沙箱测试环境实现方案提供了完整的API测试解决方案，包括：

- **完整的架构设计**：分层架构，职责清晰
- **丰富的测试功能**：支持多种测试类型和场景
- **强大的数据模拟**：灵活的数据生成和模拟能力
- **全面的监控运维**：完善的监控、日志和告警机制
- **安全可靠**：多层次的安全保障措施
- **易于部署**：支持Docker和Kubernetes部署
- **用户友好**：直观的Web界面和丰富的API

通过这个沙箱环境，开发者可以安全、高效地进行API接口的开发、测试和调试，大大提升开发效率和质量。